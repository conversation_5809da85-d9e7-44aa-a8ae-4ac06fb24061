{"checksum": "f267571636db0b579a27fae33e00b823", "roots": {"bookmark_bar": {"children": [{"date_added": "13278680945087163", "date_last_used": "13392520602702883", "guid": "1ebd529d-b490-4856-9b54-f53f828c305e", "id": "6", "name": "YT", "type": "url", "url": "https://www.youtube.com/"}, {"date_added": "13284838878031011", "date_last_used": "13391697935349246", "guid": "98cedf90-2754-498e-b066-49c941ec5385", "id": "7", "name": "WIFI Login", "type": "url", "url": "http://10.0.240.10/userportal/login.do"}, {"date_added": "13289663539470855", "date_last_used": "13392465389538536", "guid": "040be9fa-9aad-463b-b995-50dc32dcb3ac", "id": "8", "name": "Gmail", "type": "url", "url": "https://mail.google.com/mail/u/0/#inbox"}, {"date_added": "13290263384091515", "date_last_used": "13392452341239311", "guid": "2bbc3363-add0-4140-92f5-4d2f9cd5b8da", "id": "9", "name": "WhatsApp", "type": "url", "url": "https://web.whatsapp.com/"}, {"date_added": "13386103360562789", "date_last_used": "13392463861529816", "guid": "7be48a5d-71b6-4e1a-a72d-e6ba34b4ebb3", "id": "10", "meta_info": {"power_bookmark_meta": ""}, "name": "Internshala", "type": "url", "url": "https://internshala.com/internships/matching-preferences/"}, {"date_added": "13290815738442069", "date_last_used": "13360225018629136", "guid": "e57549a9-196d-4a87-9203-e420659e4d99", "id": "11", "name": "Soltn Let us c", "type": "url", "url": "https://sscresult.in/let-us-c-solutions-free-pdf-by-yash<PERSON><PERSON>-ka<PERSON><PERSON>/"}, {"date_added": "13315828567242849", "date_last_used": "13384519322173738", "guid": "c3db2953-e7ce-4c60-800c-f030f17e847a", "id": "12", "name": "Image Resizer", "type": "url", "url": "https://imageresizer.com/"}, {"date_added": "13342010180964326", "date_last_used": "13380953288615234", "guid": "6237b24b-21f9-4bf2-892c-f122f4d752b6", "id": "13", "meta_info": {"power_bookmark_meta": ""}, "name": "Image to Text", "type": "url", "url": "https://www.imagetotext.info/"}, {"date_added": "13364116505978864", "date_last_used": "0", "guid": "ca8d2d71-c61d-4a59-b0ee-b27a0dfb51cc", "id": "14", "meta_info": {"power_bookmark_meta": ""}, "name": "C++", "type": "url", "url": "https://www.pw.live/study/batches/decode-dsa-with-c---2-0-358317/subjects/decode-dsa-with-c---2-0-207674/subject-topics?came_from=my-batches&activeSection=All%20Classes&subject=DECODE%20DSA%20with%20C%2B%2B%202.0&batchSubjectId=64d35457f537a600184c131a"}, {"date_added": "13336290100455808", "date_last_used": "13392529841543751", "guid": "609d2d71-cbf7-4e1a-a906-2829cc9ccdcf", "id": "15", "name": "ChatGPT", "type": "url", "url": "https://chat.openai.com/"}, {"date_added": "13377251890798817", "date_last_used": "0", "guid": "e3e096ca-80e4-4ac2-a40d-20affcb23e05", "id": "16", "meta_info": {"power_bookmark_meta": ""}, "name": "<PERSON>", "type": "url", "url": "https://claude.ai/new"}, {"date_added": "13338544317824110", "date_last_used": "0", "guid": "1cd322ee-6f5e-4799-998d-68ba0b8ae786", "id": "17", "name": "w3", "type": "url", "url": "https://www.w3schools.com/cpp/default.asp"}, {"date_added": "13339189860652733", "date_last_used": "13392529573250466", "guid": "78afbd01-eb09-4395-ace8-9373c6321742", "id": "18", "name": "MyGitHub", "type": "url", "url": "https://github.com/"}, {"date_added": "13350832273033463", "date_last_used": "13391495983341465", "guid": "782655a8-7728-4e51-a6b2-045e3bd2a0af", "id": "19", "meta_info": {"power_bookmark_meta": ""}, "name": "leetcode", "type": "url", "url": "https://leetcode.com/problemset/"}, {"date_added": "13344255323828288", "date_last_used": "13392437015299333", "guid": "f93f535e-0e72-4dd6-a6e5-9e1b8e803b13", "id": "20", "meta_info": {"power_bookmark_meta": ""}, "name": "LinkedIn", "type": "url", "url": "https://www.linkedin.com/feed/"}, {"date_added": "13385924845307930", "date_last_used": "13392172936072809", "guid": "8349c1f5-b655-4212-803c-2829c721d96f", "id": "21", "meta_info": {"power_bookmark_meta": ""}, "name": "<PERSON>lk", "type": "url", "url": "https://www.equraninstitute.com/quranreading/067_almulk_563_565_j29.htm"}, {"date_added": "13388757926012003", "date_last_used": "13392211678710067", "guid": "80ffe3f1-e671-4d1a-a1b3-c2dab68ba45d", "id": "22", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "url", "url": "https://pwskills.com/learn/course/full-stack-web-development-1-hindi/63a2ecf58899439c8d7ebdc6/lesson/646f6fd26977ccf201decb1d/?from=%2Flearn%2F&clickText=continue&page_from=my_course_page&section=your_courses&sectionId=646f6fb76977cc6a32decb18&lectureType=video"}, {"date_added": "13361023943543330", "date_last_used": "13391914164470598", "guid": "e2540012-eddb-41c8-8d08-a799d37075c3", "id": "23", "meta_info": {"power_bookmark_meta": ""}, "name": "Quran", "type": "url", "url": "https://quran.com/"}, {"date_added": "13370957816918384", "date_last_used": "13392097461336695", "guid": "ab7bc23d-244a-4e06-804b-bbf8d0f67001", "id": "24", "meta_info": {"power_bookmark_meta": ""}, "name": "<PERSON><PERSON>", "type": "url", "url": "https://sunnah.com/"}, {"date_added": "13355514711288839", "date_last_used": "13388256281402225", "guid": "547e5978-0251-4787-b842-8f23ad9e32f7", "id": "25", "meta_info": {"power_bookmark_meta": ""}, "name": "Udemy", "type": "url", "url": "https://www.udemy.com/home/<USER>/learning/"}, {"date_added": "13347037120602264", "date_last_used": "0", "guid": "c03956a4-dfca-40fc-b758-095c08f4da42", "id": "26", "meta_info": {"power_bookmark_meta": ""}, "name": "Intranet Portal AMU", "type": "url", "url": "https://intranet.amu.ac.in/"}, {"date_added": "13340643966528171", "date_last_used": "0", "guid": "2dd4d240-0a2d-44ca-8795-50436bd4bb2a", "id": "27", "meta_info": {"power_bookmark_meta": ""}, "name": "Voicemaker® - Text to Speech Converter", "type": "url", "url": "https://voicemaker.in/"}, {"date_added": "13342295654891242", "date_last_used": "0", "guid": "e9d0fb06-6007-40cd-9919-e33da450f20d", "id": "28", "meta_info": {"power_bookmark_meta": ""}, "name": "edx Build new skills. Advance your career. | edX", "type": "url", "url": "https://www.edx.org/"}, {"date_added": "13342295704091225", "date_last_used": "0", "guid": "c0597b96-2b71-4ff3-9d9f-22b0ff01b02e", "id": "29", "meta_info": {"power_bookmark_meta": ""}, "name": "Swayam Central", "type": "url", "url": "https://swayam.gov.in/"}, {"date_added": "13351409398649567", "date_last_used": "0", "guid": "ae06aa7e-065c-418e-8e47-3b699f531b93", "id": "30", "meta_info": {"power_bookmark_meta": ""}, "name": "Responsive Web Design Certification | freeCodeCamp.org", "type": "url", "url": "https://www.freecodecamp.org/learn/2022/responsive-web-design/#learn-responsive-web-design-by-building-a-piano"}, {"date_added": "13354973666536724", "date_last_used": "0", "guid": "1ae3c707-25d6-46d8-9bd2-20ce7b8061ae", "id": "31", "meta_info": {"power_bookmark_meta": ""}, "name": "Apply to Elite US Remote Developer Jobs - Turing", "type": "url", "url": "https://developers.turing.com/enterprise"}, {"date_added": "13355106536703465", "date_last_used": "0", "guid": "2987220f-ef6c-49fd-9124-14031d50b70b", "id": "32", "meta_info": {"power_bookmark_meta": ""}, "name": "Opportunities - Instahyre", "type": "url", "url": "https://www.instahyre.com/candidate/opportunities/?matching=true"}, {"date_added": "13356145799500428", "date_last_used": "0", "guid": "86f43e10-d3f6-4fe5-b8d5-a09d101b19ae", "id": "33", "meta_info": {"power_bookmark_meta": ""}, "name": "face recog Training | Microsoft Learn", "type": "url", "url": "https://learn.microsoft.com/en-us/training/modules/analyze-images-computer-vision/6-summary#completion"}, {"date_added": "13356698426991051", "date_last_used": "0", "guid": "70985afe-e009-48a7-9819-63ee87e97547", "id": "34", "meta_info": {"power_bookmark_meta": ""}, "name": "Image Captioning", "type": "url", "url": "https://keras.io/examples/vision/image_captioning/"}, {"date_added": "13359392044013740", "date_last_used": "0", "guid": "73f108c3-e1c0-4621-83e2-2528851de5de", "id": "35", "meta_info": {"power_bookmark_meta": ""}, "name": "Practice Mock Interviews & Coding Problems - Land Top Jobs | Pramp", "type": "url", "url": "https://www.pramp.com/#/"}, {"date_added": "13364632047574358", "date_last_used": "0", "guid": "865b8bad-7da9-4738-914c-2baee022774a", "id": "36", "meta_info": {"power_bookmark_meta": ""}, "name": "Cloud Application Hosting for Developers | Render", "type": "url", "url": "https://render.com/"}, {"date_added": "13367682442043548", "date_last_used": "0", "guid": "966025a5-9849-4160-8e8a-a96247c31aca", "id": "37", "meta_info": {"power_bookmark_meta": ""}, "name": "IDX.Google", "type": "url", "url": "https://idx.google.com/"}, {"date_added": "13367768683439914", "date_last_used": "0", "guid": "52a38476-13fb-4dbd-a72c-7e7302139a18", "id": "38", "meta_info": {"power_bookmark_meta": ""}, "name": "Good First Issues", "type": "url", "url": "https://goodfirstissues.com/"}, {"date_added": "13368805222518736", "date_last_used": "0", "guid": "f006bf54-0b74-42ec-87f1-cacab9792b3c", "id": "39", "meta_info": {"power_bookmark_meta": ""}, "name": "PHP Source Code Download | Free Source Code - Diya Act", "type": "url", "url": "https://diyaact.com/php-source-code-download/"}, {"date_added": "13368887264752605", "date_last_used": "0", "guid": "631b336b-fecd-46e2-bfd1-e36ce8584606", "id": "40", "meta_info": {"power_bookmark_meta": ""}, "name": "Excalidraw | Hand-drawn look & feel • Collaborative • Secure", "type": "url", "url": "https://excalidraw.com/"}, {"date_added": "13368933312971403", "date_last_used": "0", "guid": "e1e217d4-c60f-48d9-a6b7-8a5ea7b08716", "id": "41", "meta_info": {"power_bookmark_meta": ""}, "name": "draw.io", "type": "url", "url": "https://app.diagrams.net/"}, {"date_added": "13372340624977137", "date_last_used": "0", "guid": "4465a71e-5ca1-493f-b5b3-6befeb9ec0cf", "id": "42", "meta_info": {"power_bookmark_meta": ""}, "name": "Backend Web Developer Learning Path - codedamn", "type": "url", "url": "https://codedamn.com/learning-path/backend"}, {"date_added": "13373394569171121", "date_last_used": "0", "guid": "91f0abb5-8ba7-4df5-a837-bad5b2b9d9c0", "id": "43", "meta_info": {"power_bookmark_meta": ""}, "name": "scholarship.up.gov.in", "type": "url", "url": "https://scholarship.up.gov.in/"}, {"date_added": "13381060428589652", "date_last_used": "13391496113401051", "guid": "8d4d4071-8566-4b9e-b2a6-923337af29b0", "id": "44", "meta_info": {"power_bookmark_meta": ""}, "name": "FlutterFlow", "type": "url", "url": "https://app.flutterflow.io/code/travel-app-3o9qrl?page=HomePage"}, {"date_added": "13381777021710344", "date_last_used": "0", "guid": "f323db63-95ac-4b7b-959c-34db13eaedb7", "id": "45", "meta_info": {"power_bookmark_meta": ""}, "name": "Dual N-Back", "type": "url", "url": "https://nbacking.com/"}, {"date_added": "13386961692856447", "date_last_used": "0", "guid": "d9dd4b04-fda9-45a4-b298-3f169882a42d", "id": "46", "meta_info": {"power_bookmark_meta": ""}, "name": "Dart", "type": "url", "url": "https://dart.dev/language"}, {"date_added": "13389028295625356", "date_last_used": "0", "guid": "47e4e64d-6605-427b-b4c8-783acf99dc32", "id": "47", "name": "Replacement LCD Back Cover Top Lid with Hinges for Lenovo ideapad 5-15IIL05 5-15ARE05 5-15ITL05 Series Laptop, New ideapad 5-1-15ITL05 Back Cover P/N: 5CB0X56073 AM1K7000310, <PERSON> (76830 84809) : Amazon.in: Computers & Accessories", "type": "url", "url": "https://www.amazon.in/Replacement-5-15IIL05-5-15ARE05-5-15ITL05-5-1-15ITL05/dp/B0DTK4G7X7/ref=sr_1_2?crid=23GLCZX0GHTRD&dib=eyJ2IjoiMSJ9.PHtd-hFqLckfDN8QIbjLaCKwyXO4EPf-XmIePlWzHQI9naeCyj_rsECtW894dY4MgpDfOM9xR6z7IAkH5PuvCSB7UfmAz06Atm-8kJUQhZfBtyF7Pp8-ywAGyoX6ZYp3KXrseKaWp4a1tInf_tNBhpzo6VERcGYYoo-x2nUph9xE5jqTheA3dLbRZysp-C7Xvgo3UfuPPxUSP2_W_lhdUDrD-jbxFdl-WrLv-N4r_4w.obgg-08xYgt09KSCDbD5wrNowewjgVy5BXwsPNMHZ9Q&dib_tag=se&keywords=Laptop+Parts+hinge+for+ideapad+slim+5&qid=1744552784&sprefix=laptop+parts+hinge+for+ideapad+slim+5%2Caps%2C287&sr=8-2"}, {"date_added": "13389621647955580", "date_last_used": "0", "guid": "c6ee46b4-a6ab-49a4-a30d-ccc966bf917d", "id": "48", "name": "<PERSON><PERSON><PERSON> Ex<PERSON>s", "type": "url", "url": "https://expert.chegg.com/"}, {"date_added": "13390536370377844", "date_last_used": "13392090354121303", "guid": "1a20422b-c42a-4acf-8f64-b0f6b4a2cbf0", "id": "49", "name": "02 <PERSON>ah Baqrah ( Part 1) <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON> ( <PERSON><PERSON> ul <PERSON>) - YouTube", "type": "url", "url": "https://www.youtube.com/watch?v=2v7nkZ4PFgQ&list=PLayKa92BaHdCqrIc4Kgq_OPmeGhqJNIM0&index=70"}], "date_added": "13392525588786958", "date_last_used": "0", "date_modified": "13390536370377844", "guid": "0bc5d13f-2cba-5d74-951f-3f233fe6c908", "id": "1", "name": "Bookmarks bar", "type": "folder"}, "other": {"children": [], "date_added": "13392525588786961", "date_last_used": "0", "date_modified": "0", "guid": "82b081ec-3dd3-529c-8475-ab6c344590dd", "id": "2", "name": "Other bookmarks", "type": "folder"}, "synced": {"children": [], "date_added": "13392525588786963", "date_last_used": "0", "date_modified": "0", "guid": "4cf2e351-0e85-532b-bb37-df045d8f8d0f", "id": "3", "name": "Mobile bookmarks", "type": "folder"}}, "version": 1}