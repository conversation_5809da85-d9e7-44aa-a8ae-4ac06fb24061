{"edge": {"services": {"account_id": "00034001CE59CC55", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["metricsPrivate", "systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "prefs": {"preference_reset_time": "*****************"}, "protection": {"macs": {"browser": {"show_home_button": "5776572FA26CF80A59EE6F3A424376D4E35518DB3A6441B6807E8951FA0E1B8C"}, "default_search_provider_data": {"template_url_data": "E0766A6577826AE61C179503CD5575132E804CCC62574C6CB6076163FF8D3E49"}, "edge": {"services": {"account_id": "35508B552B7615896DB72632622C2D1FC39F8DBBD4D714CBE90153EC2F70835E", "last_username": "272A316D6A16DDD79CC925C2A5F5F73D6F1162D83DB3E4438149A7FD27A9C33C"}}, "enterprise_signin": {"policy_recovery_token": "16285A7010CC83D0CC3EB381BBDF8CAA9BAE17EC2381039C4C7A1D1591CAB881"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "FC3A85E3777E30BFE697D4293AEC82AC13F832C22CEA4CD3D0EB5B980C1047E1", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "980B862D80AF80976972FE1FB380016330AE1676A8CF3058C487F1EFFAFE7E70", "neajdppkdcdipfabeoofebfddakdcjhd": "7BD4F5BE1A41D363CFDA08EAEDE708F0C5484E72EBEF002A227FEC706AFEA50E", "nkeimhogjdpnpccoofpliimaahmaaome": "259BC10B042BC519A8846CA41853E9622503EE25A8BBC489A04BA88FF5E34F39"}, "ui": {"developer_mode": "2D69A8BD6B43CD224D232C685293F403FDC42F31CC1489999BF1B6080502F201"}}, "google": {"services": {"account_id": "1B2CB271A147645D41518244ED38100688F1DDB75B9BC7365D1568B21D010187", "last_signed_in_username": "2048F2BDBBF515E5EF70A1E36C1239C921B721BC999679780F0F70EB139DAAEB", "last_username": "9FEDD0456F8E3B620E7376867180B0A282CCC681BD68B7B477FD94C14A597C61"}}, "homepage": "E990DB2C574E96AE135D463ADE098B320DBAA667EE51D581D44FF9C973DBBBB3", "homepage_is_newtabpage": "6320895ED1C49A031D20AC3739625C7DA0829557AAF3AE09589CEC0D8CA7D6D2", "media": {"cdm": {"origin_data": "BE9239A01544BD5D4ABA5B742EE165DDCC379C812F2B233BB272B691BA540B93"}, "storage_id_salt": "5E97AFE88D63F5B1E2A62DE74FEBCB38D49FC846582CB71DAABE0FB65A1DEB93"}, "module_blocklist_cache_md5_digest": "26AA4E68ACBC2B31710C921103C36A6C516163D0BEE5EB62C4F8522F93ADBF74", "pinned_tabs": "316857D581B8CCB91A4A19295047D69D5987D2A61115B0AB5E4B718AB62E7DAA", "prefs": {"preference_reset_time": "C21C9B7FE449EB01D3CCFD91E809A8803D3193EEDE88B89A242CC0D1891AA00C"}, "safebrowsing": {"incidents_sent": "B78E921ACCD38A5C0A95C1A8F347645B4DE91B89440973A67774E52735D0BE24"}, "search_provider_overrides": "001FCF3CEB0C9E969B560C299B14BA9FC0B565AC2F98D1884EAE8796BDA8F5A9", "session": {"restore_on_startup": "54EB8DCAFD4DBB87C7BE9CEA969B6EBDE3C04E553EC01C2712EE8E791254BB3E", "startup_urls": "1DD7EF9F2C566399B0F95547DF233603B2882A60175C6033BF16D1D7EAFAAA77"}}, "super_mac": "C5EC0456B5688375091B7660B17DAEA9743E79CE5C32A3A8170780B0B17033D2"}, "safebrowsing": {"incidents_sent": {"1": {"default_search_provider_data.template_url_data": "*********", "extensions.settings": "**********", "google.services.account_id": "*********", "google.services.last_signed_in_username": "*********", "google.services.last_username": "**********", "prefs.preference_reset_time": "*********"}}}, "session": {"restore_on_startup_edge_lightweight": "59FA641665368DE286F4CC4ACB467DC12611372E23221DAE7023CE1476", "restore_on_startup_edge_lightweight_verify": "b2d7a00a6ad3f613108fd0f5ca0519a9", "startup_urls_edge_lightweight": "A119AF8A73A4C5FE343ED30F6040445EE7DB558D37F53B69434CF113AA4DF848C3735E013FD60588D9114BDD5DA7058932D874BAB6ECF491CF2803EA35F524D14474D061A51BF2", "startup_urls_edge_lightweight_verify": "1f81504076a79e382ddb49fb4986eb55"}}